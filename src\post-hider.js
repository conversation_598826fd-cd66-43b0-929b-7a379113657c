/**
 * 微博隐藏功能模块
 * 检测并隐藏黑名单用户的微博，支持动态加载
 */

export class PostHider {
  constructor(blacklistStorage, userExtractor) {
    this.blacklistStorage = blacklistStorage;
    this.userExtractor = userExtractor;
    this.observer = null;
    this.hiddenPosts = new Set();
    
    this.init();
  }

  /**
   * 初始化微博隐藏功能
   */
  init() {
    this.hideExistingPosts();
    this.observeNewPosts();
    this.bindEvents();
  }

  /**
   * 隐藏当前页面已存在的微博
   */
  hideExistingPosts() {
    const articles = this.userExtractor.getAllArticles();

    Array.from(articles).forEach(article => {
      if (this.shouldHidePost(article)) {
        this.hidePost(article);
      }
    });
  }

  /**
   * 监听新加载的微博
   */
  observeNewPosts() {
    this.observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查是否是微博文章
              if (node.matches && node.matches('article.Feed_wrap_3v9LH')) {
                if (this.shouldHidePost(node)) {
                  this.hidePost(node);
                }
              }

              // 检查子元素中是否有微博文章
              const articles = node.querySelectorAll && node.querySelectorAll('article.Feed_wrap_3v9LH');
              if (articles) {
                Array.from(articles).forEach(article => {
                  if (this.shouldHidePost(article)) {
                    this.hidePost(article);
                  }
                });
              }
            }
          });
        }
      });
    });

    // 观察整个文档的变化
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听用户被拉黑事件
    document.addEventListener('userBlacklisted', (e) => {
      const { userInfo } = e.detail;
      this.hideUserPosts(userInfo.userId);
    });

    // 监听用户被取消拉黑事件
    document.addEventListener('userUnblacklisted', (e) => {
      const { userInfo } = e.detail;
      this.showUserPosts(userInfo.userId);
    });
  }

  /**
   * 判断是否应该隐藏微博
   * @param {Element} article - 微博文章DOM元素
   * @returns {boolean} 是否应该隐藏
   */
  shouldHidePost(article) {
    const userInfo = this.userExtractor.extractUserInfo(article);
    return userInfo && this.blacklistStorage.isBlacklisted(userInfo.username);
  }

  /**
   * 隐藏微博
   * @param {Element} article - 微博文章DOM元素
   */
  hidePost(article) {
    const userInfo = this.userExtractor.extractUserInfo(article);
    if (!userInfo) return;

    // 避免重复隐藏
    if (this.hiddenPosts.has(article)) return;

    const username = userInfo.username;

    // 创建隐藏容器
    const hiddenContainer = document.createElement('div');
    hiddenContainer.className = 'weibo-enhancer-hidden-post';
    hiddenContainer.style.cssText = `
      background: #f5f5f5;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 16px;
      margin: 8px 0;
      text-align: center;
      color: #666;
      font-size: 14px;
      position: relative;
    `;

    hiddenContainer.innerHTML = `
      <div style="margin-bottom: 8px;">
        <span style="color: #999;">已屏蔽用户: ${username}</span>
      </div>
      <div style="display: flex; gap: 8px; justify-content: center; align-items: center;">
        <button class="show-post-btn" style="
          background: #1890ff;
          color: white;
          border: none;
          padding: 4px 12px;
          border-radius: 3px;
          cursor: pointer;
          font-size: 12px;
        ">显示此微博</button>
        <button class="unblock-user-btn" style="
          background: #52c41a;
          color: white;
          border: none;
          padding: 4px 12px;
          border-radius: 3px;
          cursor: pointer;
          font-size: 12px;
        ">取消屏蔽</button>
      </div>
    `;

    // 绑定按钮事件
    const showBtn = hiddenContainer.querySelector('.show-post-btn');
    const unblockBtn = hiddenContainer.querySelector('.unblock-user-btn');

    showBtn.addEventListener('click', () => {
      this.showPost(article, hiddenContainer);
    });

    unblockBtn.addEventListener('click', () => {
      this.blacklistStorage.removeUser(username);
      this.showPost(article, hiddenContainer);

      // 触发事件
      document.dispatchEvent(new CustomEvent('userUnblacklisted', {
        detail: { userInfo: { username } }
      }));
    });

    // 保存原始文章引用
    hiddenContainer._originalArticle = article;

    // 隐藏原文章并插入隐藏容器
    article.style.display = 'none';
    article.parentNode.insertBefore(hiddenContainer, article);

    this.hiddenPosts.add(article);
  }

  /**
   * 显示微博
   * @param {Element} article - 微博文章DOM元素
   * @param {Element} hiddenContainer - 隐藏容器元素
   */
  showPost(article, hiddenContainer) {
    article.style.display = '';
    if (hiddenContainer && hiddenContainer.parentNode) {
      hiddenContainer.parentNode.removeChild(hiddenContainer);
    }
    this.hiddenPosts.delete(article);
  }

  /**
   * 隐藏指定用户的所有微博
   * @param {string} username - 用户名
   */
  hideUserPosts(username) {
    const articles = this.userExtractor.findArticlesByUsername(username);

    articles.forEach(article => {
      if (!this.hiddenPosts.has(article)) {
        this.hidePost(article);
      }
    });
  }

  /**
   * 显示指定用户的所有微博
   * @param {string} username - 用户名
   */
  showUserPosts(username) {
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');

    Array.from(hiddenContainers).forEach(container => {
      const article = container._originalArticle;
      if (article) {
        const userInfo = this.userExtractor.extractUserInfo(article);
        if (userInfo && userInfo.username === username) {
          this.showPost(article, container);
        }
      }
    });
  }

  /**
   * 获取隐藏统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');
    return {
      hiddenPostsCount: this.hiddenPosts.size,
      hiddenContainersCount: hiddenContainers.length,
      blacklistedUsersCount: this.blacklistStorage.getAllUsers().length
    };
  }

  /**
   * 销毁微博隐藏功能
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }

    // 显示所有隐藏的微博
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');
    Array.from(hiddenContainers).forEach(container => {
      const article = container._originalArticle;
      if (article) {
        this.showPost(article, container);
      }
    });

    this.hiddenPosts.clear();
  }
}
