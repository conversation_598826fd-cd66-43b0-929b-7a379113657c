import { defineConfig } from 'vite'
import monkey from 'vite-plugin-monkey'

export default defineConfig({
  plugins: [
    monkey({
      entry: 'src/main.js',
      userscript: {
        name: 'weibo enhancer',
        namespace: 'weibo-enhancer',
        description: '<PERSON><PERSON><PERSON> with blacklist functionality',
        author: 'weibo-enhancer',
        version: '0.0.0',
        match: ['https://weibo.com/hot/weibo/*'],
        grant: ['GM_setValue', 'GM_getValue', 'GM_registerMenuCommand'],
        'run-at': 'document-end'
      },
    }),
  ],
})
