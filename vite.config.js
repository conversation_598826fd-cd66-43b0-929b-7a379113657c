import { defineConfig } from 'vite'
import monkey from 'vite-plugin-monkey'

export default defineConfig({
  plugins: [
    monkey({
      entry: 'src/main.js',
      userscript: {
        name: 'weibo enhancer',
        match: ['https://weibo.com/hot/weibo/*'],
        grant: ['GM_setValue', 'GM_getValue', 'GM_registerMenuCommand'],
        'run-at': 'document-end',
      },
    }),
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      format: {
        comments: false,
      },
    },
  },
})
