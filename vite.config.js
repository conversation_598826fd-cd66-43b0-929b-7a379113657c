import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';

export default defineConfig({
  plugins: [
    monkey({
      entry: 'src/main.js',
      userscript: {
        name: 'Hupu Enhancer',
        match: ['https://bbs.hupu.com/*'],
        grant: ['GM_setValue', 'GM_getValue', 'GM_registerMenuCommand'],
        'run-at': 'document-start',
      },
    }),
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      format: {
        comments: false,
      },
    },
  },
});