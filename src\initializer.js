import { hidePosts } from './userBlocker.js';
import { hideImages, setupImageListeners } from './imageBlocker.js';
import { hideMenus } from './menuManager.js';
import { initCompactLayout, applyCompactLayout } from './layoutOptimizer.js';

let mutationTimeout;
// 防抖函数，用于限制 DOM 变化的监听频率，避免频繁触发回调
function debounceMutations(callback) {
    clearTimeout(mutationTimeout); // 清除之前的定时器
    mutationTimeout = setTimeout(callback, 50); // 50ms 后执行回调
}

// 设置 MutationObserver 监听 DOM 变化
function setupObservers() {
    const observer = new MutationObserver(() => {
        // 移除防抖，直接执行隐藏操作
        hidePosts(); // 隐藏屏蔽用户帖子
        hideImages(); // 隐藏屏蔽图片
        setupImageListeners(); // 重新绑定图片监听

        // 只有在紧凑布局启用时才重新应用（默认启用，除非明确禁用）
        const setting = localStorage.getItem('hupu-enhancer-compact-layout');
        if (setting !== 'disabled') {
            applyCompactLayout();
        }
    });

    // 监听页面及其子节点变化
    observer.observe(document.body, {
        childList: true, // 监听子节点的增减
        subtree: true // 监听所有后代节点的增减
    });
}

function _runInitializations() {
    hidePosts(); // 隐藏屏蔽帖子
    hideImages(); // 隐藏屏蔽图片
    setupImageListeners(); // 绑定图片监听
    setupObservers(); // 初始化 DOM 监听
    initCompactLayout(); // 根据用户设置初始化紧凑布局
}

// 脚本初始化函数
export function initializeScript() {
    // 无论页面加载状态如何，立即运行初始化函数
    _runInitializations();

    // 监听页面点击事件以隐藏菜单
    document.addEventListener('click', (e) => {
        // userMenu 和 imageMenu 的判断将在 menuManager.js 中处理
        hideMenus(e);
    });

    // 监听页面右键事件以隐藏菜单（在其他右键菜单显示前）
    document.addEventListener('contextmenu', (e) => {
        // 如果不是在用户链接或图片上右键，则隐藏所有菜单
        const isUserLink = e.target.closest('a[href^="https://my.hupu.com/"]');
        const isImage = e.target.closest('img.thread-img');

        if (!isUserLink && !isImage) {
            hideMenus(e);
        }
    }, { capture: true }); // 使用捕获阶段确保在其他事件处理前执行

    // 监听页面滚动事件以隐藏菜单
    document.addEventListener('wheel', hideMenus, { passive: true });
} 