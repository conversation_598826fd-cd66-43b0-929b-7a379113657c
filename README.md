# Hupu Enhancer

## Features

1. **User Blocking System**
   - Block specific users by username
   - Hide all posts, replies, and quotes from blocked users
   - Right-click on usernames to quickly add to blocklist
   - Manage blocked users through a dedicated modal interface

2. **Image Blocking System**
   - Block images by URL or URL prefix
   - Right-click on images to add to blocklist
   - Hide blocked images and their containers automatically

3. **User Management Interface**
   - Dedicated modal for managing blocked users
   - Add/remove users from blocklist
   - One username per line input format
   - Automatic deduplication when saving
   - Real-time content restoration when users are removed from blocklist

4. **Compact Layout Mode**
   - Toggle between normal and compact post layouts
   - Reduces spacing and font sizes for more content on screen
   - Enabled by default with user preference persistence
   - Accessible through the user management modal

5. **Persistent Storage**
   - All settings saved using Greasemonkey storage API
   - Blocked users and images persist across browser sessions
   - Layout preferences remembered

6. **Real-time Content Processing**
   - Automatic hiding of blocked content on page load
   - Dynamic processing of new content as it loads
   - MutationObserver for detecting DOM changes

7. **Context Menu Integration**
   - Right-click menus for quick user/image blocking
   - Smart menu positioning to stay within viewport
   - Automatic menu hiding on scroll or click outside

8. **Immediate UI Feedback**
   - Content appears/disappears immediately when blocklist is updated
   - Visual feedback for save operations
   - No page refresh required

9. **Cross-page Functionality**
   - Works across all Hupu forum pages
   - Consistent blocking behavior throughout the site

10. **Performance Optimized**
    - Vanilla JavaScript implementation (no framework dependencies)
    - Efficient DOM querying and manipulation
    - Minimal memory footprint
