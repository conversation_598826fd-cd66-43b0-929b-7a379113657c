/**
 * Weibo Enhancer - Custom blacklist system for Weibo hot posts
 * 自定义黑名单系统版本
 */

import { BlacklistStorage } from './blacklist-storage.js';
import { UserExtractor } from './user-extractor.js';
import { ContextMenu } from './context-menu.js';
import { PostHider } from './post-hider.js';
import { BlacklistModal } from './blacklist-modal.js';

class WeiboEnhancer {
  constructor() {
    this.blacklistStorage = null;
    this.userExtractor = null;
    this.contextMenu = null;
    this.postHider = null;
    this.blacklistModal = null;

    this.init();
  }

  /**
   * 初始化增强器
   */
  init() {
    // 等待页面加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * 启动增强器
   */
  start() {
    try {
      // 初始化核心模块
      this.blacklistStorage = new BlacklistStorage();
      this.userExtractor = new UserExtractor();

      // 初始化功能模块
      this.contextMenu = new ContextMenu(this.blacklistStorage, this.userExtractor);
      this.postHider = new PostHider(this.blacklistStorage, this.userExtractor);
      this.blacklistModal = new BlacklistModal(this.blacklistStorage, this.postHider);

      // 注册油猴菜单
      this.registerUserscriptMenu();

    } catch (error) {
      // 静默失败
    }
  }

  /**
   * 注册油猴脚本菜单
   */
  registerUserscriptMenu() {
    if (typeof GM_registerMenuCommand !== 'undefined') {
      GM_registerMenuCommand('Blocklist', () => {
        this.blacklistModal.show();
      });
    }
  }

  /**
   * 销毁增强器，清理资源
   */
  destroy() {
    if (this.contextMenu) {
      this.contextMenu.destroy();
    }

    if (this.postHider) {
      this.postHider.destroy();
    }

    if (this.blacklistModal) {
      this.blacklistModal.destroy();
    }
  }
}

// 初始化增强器
const weiboEnhancer = new WeiboEnhancer();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  weiboEnhancer.destroy();
});
