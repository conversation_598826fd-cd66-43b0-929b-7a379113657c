import { getBlockedUsers, addBlockedUser } from './storage.js';
import { userMenu, hideMenus } from './menuManager.js';

// 隐藏屏蔽用户的所有内容（帖子、回复和引用）
export function hidePosts() {
    const blockedUsers = getBlockedUsers();

    // 隐藏帖子和回复 - 查找帖子或回复中的用户链接，排除引用中的链接
    const posts = Array.from(document.querySelectorAll('a[href^="https://my.hupu.com/"]'))
        .filter(a => /^https:\/\/my\.hupu\.com\/\w+$/.test(a.href) && !a.closest('div > div > span'));

    posts.forEach(post => {
        const username = post.textContent.trim();
        if (blockedUsers.includes(username)) {
            const postLi = post.closest('li.bbs-sl-web-post-body');
            const postDiv = post.closest('div[class^=post-content_bbs-post-content]');
            const replyDiv = post.closest('.post-reply-list-wrapper');
            if (postLi) postLi.classList.add('hupu-enhancer-hidden');
            if (postDiv) postDiv.classList.add('hupu-enhancer-hidden');
            if (replyDiv) replyDiv.classList.add('hupu-enhancer-hidden');
        }
    });

    // 隐藏屏蔽用户的引用
    const quoteContainers = document.querySelectorAll('div > div > span > a[href^="https://my.hupu.com/"]');
    quoteContainers.forEach(quote => {
        const username = quote.textContent.trim();
        if (blockedUsers.includes(username)) {
            const quoteText = quote.closest('div[class*="quote-text"]');
            const replyThread = quote.closest('div[class*="thread-comp-container"]')?.querySelector('div[class*="reply-thread"]');
            const toggleThread = quote.closest('div[class*="thread-comp-container"]')?.querySelector('div[class*="toggle-thread"]');
            if (quoteText) quoteText.classList.add('hupu-enhancer-hidden');
            if (replyThread) replyThread.classList.add('hupu-enhancer-hidden');
            if (toggleThread) toggleThread.classList.add('hupu-enhancer-hidden');
        }
    });
}

// 恢复指定用户的所有内容（帖子、回复和引用）
export function restoreUserPosts(usernames) {
    if (!Array.isArray(usernames)) {
        usernames = [usernames];
    }

    // 恢复帖子和回复 - 查找帖子或回复中的用户链接，排除引用中的链接
    const posts = Array.from(document.querySelectorAll('a[href^="https://my.hupu.com/"]'))
        .filter(a => /^https:\/\/my\.hupu\.com\/\w+$/.test(a.href) && !a.closest('div > div > span'));

    posts.forEach(post => {
        const username = post.textContent.trim();
        if (usernames.includes(username)) {
            const postLi = post.closest('li.bbs-sl-web-post-body');
            const postDiv = post.closest('div[class^=post-content_bbs-post-content]');
            const replyDiv = post.closest('.post-reply-list-wrapper');
            if (postLi) postLi.classList.remove('hupu-enhancer-hidden');
            if (postDiv) postDiv.classList.remove('hupu-enhancer-hidden');
            if (replyDiv) replyDiv.classList.remove('hupu-enhancer-hidden');
        }
    });

    // 恢复屏蔽用户的引用
    const quoteContainers = document.querySelectorAll('div > div > span > a[href^="https://my.hupu.com/"]');
    quoteContainers.forEach(quote => {
        const username = quote.textContent.trim();
        if (usernames.includes(username)) {
            const quoteText = quote.closest('div[class*="quote-text"]');
            const replyThread = quote.closest('div[class*="thread-comp-container"]')?.querySelector('div[class*="reply-thread"]');
            const toggleThread = quote.closest('div[class*="thread-comp-container"]')?.querySelector('div[class*="toggle-thread"]');
            if (quoteText) quoteText.classList.remove('hupu-enhancer-hidden');
            if (replyThread) replyThread.classList.remove('hupu-enhancer-hidden');
            if (toggleThread) toggleThread.classList.remove('hupu-enhancer-hidden');
        }
    });
}

// 将用户添加到屏蔽列表开头并立即隐藏该用户的所有内容
function blockUser(username) {
    addBlockedUser(username);
    // 立即隐藏该用户的所有内容（帖子、回复和引用）
    hidePosts();
}

document.addEventListener('contextmenu', (event) => {
    const target = event.target.closest('a[href^="https://my.hupu.com/"]');
    if (target) {
        event.preventDefault();
        const username = target.textContent.trim();
        if (!username) return;

        // 先隐藏所有菜单，再显示用户菜单
        hideMenus();

        userMenu.textContent = `屏蔽 ${username}`;

        const menuWidth = userMenu.offsetWidth || 100;
        const menuHeight = userMenu.offsetHeight || 30;
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let adjustedLeft = event.clientX;
        let adjustedTop = event.clientY;

        if (adjustedLeft + menuWidth > viewportWidth) {
            adjustedLeft = viewportWidth - menuWidth - 5;
        }
        if (adjustedTop + menuHeight > viewportHeight) {
            adjustedTop = viewportHeight - menuHeight - 5;
        }

        adjustedLeft = Math.max(5, adjustedLeft);
        adjustedTop = Math.max(5, adjustedTop);

        userMenu.style.left = `${adjustedLeft}px`;
        userMenu.style.top = `${adjustedTop}px`;
        userMenu.style.display = 'block';

        userMenu.onclick = () => {
            blockUser(username);
            userMenu.style.display = 'none';
        };
    }
});