/**
 * 黑名单模态框组件 - 使用Shadow DOM
 */

export class BlacklistModal {
  constructor(blacklistStorage, postHider) {
    this.blacklistStorage = blacklistStorage;
    this.postHider = postHider;
    this.isVisible = false;
    this.shadowHost = null;
    this.shadowRoot = null;

    this.createModal();
    this.bindEvents();
  }

  createModal() {
    // 创建Shadow DOM宿主元素
    this.shadowHost = document.createElement('div');
    this.shadowHost.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 99999999;
      display: none;
      pointer-events: auto;
    `;

    // 创建Shadow Root
    this.shadowRoot = this.shadowHost.attachShadow({ mode: 'closed' });

    // 创建样式
    const style = document.createElement('style');
    style.textContent = `
      :host {
        all: initial;
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 99999999;
        pointer-events: auto;
      }

      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0, 0, 0, 0.3);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .modal {
        background: white;
        padding: 24px;
        border-radius: 16px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        width: 100%;
        max-width: 380px;
        min-height: 420px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e5e7eb;
      }

      .header {
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
        margin-bottom: 16px;
      }

      .title {
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        color: #374151;
        margin: 0;
      }

      .textarea {
        flex: 1;
        resize: none;
        border-radius: 6px;
        border: 2px solid #d1d5db;
        padding: 8px;
        font-size: 15px;
        outline: none;
        background: white;
        font-family: monospace;
        margin-bottom: 12px;
        min-height: 160px;
        scrollbar-width: thin;
        scrollbar-color: #999 transparent;
        pointer-events: auto;
        user-select: text;
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
      }

      .textarea:focus {
        border-color: #9ca3af;
      }

      .textarea::-webkit-scrollbar {
        width: 8px;
      }

      .textarea::-webkit-scrollbar-track {
        background: transparent;
      }

      .textarea::-webkit-scrollbar-thumb {
        background: #999;
        border-radius: 4px;
      }

      .buttons {
        display: flex;
        justify-content: center;
        gap: 24px;
        margin-top: 8px;
      }

      .button {
        width: 96px;
        border-radius: 6px;
        border: 2px solid #d1d5db;
        padding: 4px 0;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s;
        background: white;
        color: #374151;
        pointer-events: auto;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      .button:hover {
        border-color: #3b82f6;
        background-color: #dbeafe;
      }

      .button.close:hover {
        border-color: #f87171;
        background-color: #fee2e2;
      }

      .button.saved {
        border-color: #10b981 !important;
        background-color: #d1fae5 !important;
      }
    `;

    // 创建模态框HTML结构
    const modalHTML = `
      <div class="overlay">
        <div class="modal">
          <div class="header">
            <h3 class="title">用户黑名单</h3>
          </div>

          <textarea class="textarea" placeholder="请输入要屏蔽的用户名，一行一个..."></textarea>

          <div class="buttons">
            <button class="button save">保存</button>
            <button class="button close">关闭</button>
          </div>
        </div>
      </div>
    `;

    // 添加样式和内容到Shadow DOM
    this.shadowRoot.appendChild(style);
    this.shadowRoot.innerHTML += modalHTML;

    // 添加到页面
    document.body.appendChild(this.shadowHost);

    // 获取元素引用
    this.overlay = this.shadowRoot.querySelector('.overlay');
    this.textarea = this.shadowRoot.querySelector('.textarea');
    this.saveBtn = this.shadowRoot.querySelector('.save');
    this.closeBtn = this.shadowRoot.querySelector('.close');

  }

  bindEvents() {
    // 关闭按钮
    this.closeBtn.addEventListener('click', () => this.hide());

    // 保存按钮
    this.saveBtn.addEventListener('click', () => this.save());

    // 移除点击遮罩层关闭功能
    // this.overlay.addEventListener('click', (e) => {
    //   if (e.target === this.overlay) {
    //     this.hide();
    //   }
    // });

    // ESC键关闭
    this.escapeHandler = (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    };
    document.addEventListener('keydown', this.escapeHandler);
  }

  show() {
    this.isVisible = true;
    this.shadowHost.style.display = 'block';
    this.shadowHost.style.pointerEvents = 'auto';
    this.loadData();
    // 延迟聚焦以确保元素可见
    setTimeout(() => {
      if (this.textarea) {
        this.textarea.focus();
      }
    }, 50);
  }

  hide() {
    this.isVisible = false;
    this.shadowHost.style.display = 'none';
    this.shadowHost.style.pointerEvents = 'none';
  }

  loadData() {
    const usernames = this.blacklistStorage.getUsernames();
    this.textarea.value = usernames.join('\n');
  }

  save() {
    const text = this.textarea.value;
    const usernames = text.split('\n')
      .map(line => line.trim())
      .filter(line => line);

    // 去重
    const uniqueUsernames = [...new Set(usernames)];

    // 获取当前黑名单
    const currentUsernames = this.blacklistStorage.getUsernames();

    // 找出被移除的用户
    const removedUsers = currentUsernames.filter(name => !uniqueUsernames.includes(name));

    // 保存新的黑名单
    this.blacklistStorage.setUsernames(uniqueUsernames);

    // 恢复被移除用户的微博
    if (removedUsers.length > 0) {
      removedUsers.forEach(username => {
        this.postHider.showUserPosts(username);
      });
    }

    // 隐藏新增用户的微博
    uniqueUsernames.forEach(username => {
      this.postHider.hideUserPosts(username);
    });

    // 显示保存成功状态
    const originalText = this.saveBtn.textContent;
    this.saveBtn.textContent = '已保存';
    this.saveBtn.classList.add('saved');

    setTimeout(() => {
      this.saveBtn.textContent = originalText;
      this.saveBtn.classList.remove('saved');
    }, 1500);
  }

  destroy() {
    // 移除事件监听器
    if (this.escapeHandler) {
      document.removeEventListener('keydown', this.escapeHandler);
    }

    // 移除Shadow DOM宿主元素
    if (this.shadowHost && this.shadowHost.parentNode) {
      this.shadowHost.parentNode.removeChild(this.shadowHost);
    }
  }
}
