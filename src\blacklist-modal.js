/**
 * 黑名单模态框组件
 */

export class BlacklistModal {
  constructor(blacklistStorage, postHider) {
    this.blacklistStorage = blacklistStorage;
    this.postHider = postHider;
    this.isVisible = false;
    
    this.createModal();
    this.bindEvents();
  }

  createModal() {
    // 创建模态框容器
    this.overlay = document.createElement('div');
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 99999999;
      display: none;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.3);
    `;

    // 创建模态框内容
    this.overlay.innerHTML = `
      <div style="
        background: white;
        padding: 24px;
        border-radius: 16px;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        width: 100%;
        max-width: 380px;
        min-height: 420px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e5e7eb;
      ">
        <div style="
          border-bottom: 1px solid #eee;
          padding-bottom: 8px;
          margin-bottom: 16px;
        ">
          <h3 style="
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            color: #374151;
            margin: 0;
          ">用户黑名单</h3>
        </div>
        
        <textarea id="blacklist-textarea" style="
          flex: 1;
          resize: none;
          border-radius: 6px;
          border: 2px solid #d1d5db;
          padding: 8px;
          font-size: 15px;
          outline: none;
          background: white;
          font-family: monospace;
          margin-bottom: 12px;
          min-height: 160px;
          scrollbar-width: thin;
          scrollbar-color: #999 transparent;
        " placeholder="请输入要屏蔽的用户名，一行一个..."></textarea>

        <div style="
          display: flex;
          justify-content: center;
          gap: 24px;
          margin-top: 8px;
        ">
          <button id="save-blacklist-btn" style="
            width: 96px;
            border-radius: 6px;
            border: 2px solid #d1d5db;
            padding: 4px 0;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
          ">保存</button>
          <button id="close-blacklist-btn" style="
            width: 96px;
            border-radius: 6px;
            border: 2px solid #d1d5db;
            padding: 4px 0;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            background: white;
          ">关闭</button>
        </div>
      </div>
    `;

    document.body.appendChild(this.overlay);

    // 获取元素引用
    this.textarea = this.overlay.querySelector('#blacklist-textarea');
    this.saveBtn = this.overlay.querySelector('#save-blacklist-btn');
    this.closeBtn = this.overlay.querySelector('#close-blacklist-btn');

    // 添加按钮悬停效果
    this.saveBtn.addEventListener('mouseenter', () => {
      this.saveBtn.style.borderColor = '#3b82f6';
      this.saveBtn.style.backgroundColor = '#dbeafe';
    });
    this.saveBtn.addEventListener('mouseleave', () => {
      this.saveBtn.style.borderColor = '#d1d5db';
      this.saveBtn.style.backgroundColor = 'white';
    });

    this.closeBtn.addEventListener('mouseenter', () => {
      this.closeBtn.style.borderColor = '#f87171';
      this.closeBtn.style.backgroundColor = '#fee2e2';
    });
    this.closeBtn.addEventListener('mouseleave', () => {
      this.closeBtn.style.borderColor = '#d1d5db';
      this.closeBtn.style.backgroundColor = 'white';
    });

    // 聚焦样式
    this.textarea.addEventListener('focus', () => {
      this.textarea.style.borderColor = '#9ca3af';
    });
    this.textarea.addEventListener('blur', () => {
      this.textarea.style.borderColor = '#d1d5db';
    });
  }

  bindEvents() {
    // 关闭按钮
    this.closeBtn.addEventListener('click', () => this.hide());

    // 保存按钮
    this.saveBtn.addEventListener('click', () => this.save());

    // ESC键关闭
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  show() {
    this.isVisible = true;
    this.overlay.style.display = 'flex';
    this.loadData();
    this.textarea.focus();
  }

  hide() {
    this.isVisible = false;
    this.overlay.style.display = 'none';
  }

  loadData() {
    const usernames = this.blacklistStorage.getUsernames();
    this.textarea.value = usernames.join('\n');
  }

  save() {
    const text = this.textarea.value;
    const usernames = text.split('\n')
      .map(line => line.trim())
      .filter(line => line);

    // 去重
    const uniqueUsernames = [...new Set(usernames)];

    // 获取当前黑名单
    const currentUsernames = this.blacklistStorage.getUsernames();

    // 找出被移除的用户
    const removedUsers = currentUsernames.filter(name => !uniqueUsernames.includes(name));

    // 保存新的黑名单
    this.blacklistStorage.setUsernames(uniqueUsernames);

    // 恢复被移除用户的微博
    if (removedUsers.length > 0) {
      removedUsers.forEach(username => {
        this.postHider.showUserPosts(username);
      });
    }

    // 隐藏新增用户的微博
    uniqueUsernames.forEach(username => {
      this.postHider.hideUserPosts(username);
    });

    // 显示保存成功状态
    const originalText = this.saveBtn.textContent;
    this.saveBtn.textContent = '已保存';
    this.saveBtn.style.borderColor = '#10b981';
    this.saveBtn.style.backgroundColor = '#d1fae5';

    setTimeout(() => {
      this.saveBtn.textContent = originalText;
      this.saveBtn.style.borderColor = '#d1d5db';
      this.saveBtn.style.backgroundColor = 'white';
    }, 1500);
  }

  destroy() {
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
  }
}
