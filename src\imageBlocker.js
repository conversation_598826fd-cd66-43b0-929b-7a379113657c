import { getBlockedImages, addBlockedImage } from './storage.js';
import { imageMenu } from './menuManager.js';
import { hideMenus } from './menuManager.js';

let currentImg = null; // 存储当前右键点击的图片对象

// 隐藏屏蔽的图片
export function hideImages() {
    const blockedImages = getBlockedImages();
    blockedImages.forEach(src => { // 遍历屏蔽图片列表
        // 查找匹配图片元素
        document.querySelectorAll(`img[src^="${src}"]`).forEach(img => {
            // 获取图片父容器
            const container = img.closest('[class*="thread-img-container"]') || img.parentElement;
            if (container) container.classList.add('hupu-enhancer-hidden'); // 隐藏容器
        });
    });
}

// 为图片元素绑定右键事件监听
export function setupImageListeners() {
    // 选择未绑定监听的图片
    document.querySelectorAll('img.thread-img:not([data-block-listener])').forEach(img => {
        img.addEventListener('contextmenu', handleImageContextMenu, { capture: true }); // 绑定右键事件
        img.setAttribute('data-block-listener', 'true'); // 标记已绑定
    });
}

// 处理图片右键菜单显示
function handleImageContextMenu(e) {
    let target = e.target; // 获取触发元素
    // 向上查找图片元素
    while (target && target.nodeName !== 'IMG') {
        target = target.parentElement;
    }

    if (target && target.classList.contains('thread-img')) { // 确认目标图片
        e.preventDefault(); // 阻止默认右键菜单
        e.stopPropagation(); // 阻止事件冒泡

        // 先隐藏所有菜单，再显示图片菜单
        hideMenus();

        // 存储当前图片及其容器
        currentImg = {
            element: target,
            container: target.closest('[class*="thread-img-container"]') || target.parentElement
        };

        // 计算菜单尺寸
        const menuWidth = imageMenu.offsetWidth || 160;
        const menuHeight = imageMenu.offsetHeight || 44;
        const viewportWidth = window.innerWidth; // 视口宽度
        const viewportHeight = window.innerHeight; // 视口高度

        // 获取鼠标点击位置
        let adjustedLeft = e.clientX;
        let adjustedTop = e.clientY;

        // 确保菜单不超出视口
        if (adjustedLeft + menuWidth > viewportWidth) {
            adjustedLeft = viewportWidth - menuWidth - 5; // 保留 5px 边距
        }
        if (adjustedTop + menuHeight > viewportHeight) {
            adjustedTop = viewportHeight - menuHeight - 5;
        }

        // 防止菜单超出左侧或顶部
        adjustedLeft = Math.max(5, adjustedLeft);
        adjustedTop = Math.max(5, adjustedTop);

        // 设置图片菜单位置并显示
        imageMenu.style.left = `${adjustedLeft}px`;
        imageMenu.style.top = `${adjustedTop}px`;
        imageMenu.style.display = 'block';
    }
}

// 处理图片屏蔽菜单点击事件
imageMenu.querySelector('.menu-item').addEventListener('click', () => {
    if (!currentImg) return; // 无当前图片则退出

    const src = currentImg.element.src; // 获取图片 URL
    const cleanSrc = src.replace(/(?:\?|&)x-oss-process=.*$/, ''); // 移除 URL 参数

    addBlockedImage(cleanSrc); // 添加到屏蔽列表并保存
    hideImages(); // 隐藏匹配图片

    // 隐藏图片或其容器
    if (currentImg.container) {
        currentImg.container.classList.add('hupu-enhancer-hidden');
    } else {
        currentImg.element.classList.add('hupu-enhancer-hidden');
    }

    hideMenus(); // 隐藏所有菜单
    currentImg = null; // 清空当前图片
}); 