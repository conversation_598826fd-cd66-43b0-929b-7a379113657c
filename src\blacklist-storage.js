/**
 * 黑名单存储管理模块
 * 使用油猴脚本GM_setValue/GM_getValue管理黑名单数据
 */

const BLACKLIST_KEY = 'weibo_enhancer_blacklist';

export class BlacklistStorage {
  constructor() {
    this.blacklist = this.loadBlacklist();
  }

  /**
   * 从GM storage加载黑名单
   * @returns {Array} 黑名单数组
   */
  loadBlacklist() {
    try {
      const data = GM_getValue(BLACKLIST_KEY, '');
      if (!data) return [];

      // 支持一行一个名字的格式
      const lines = data.split('\n').map(line => line.trim()).filter(line => line);
      return lines.map(username => ({
        userId: username, // 使用用户名作为ID
        username: username,
        addedAt: new Date().toISOString()
      }));
    } catch (error) {
      return [];
    }
  }

  /**
   * 保存黑名单到GM storage
   */
  saveBlacklist() {
    try {
      // 保存为一行一个名字的格式
      const usernames = this.blacklist.map(user => user.username);
      GM_setValue(BLACKLIST_KEY, usernames.join('\n'));
    } catch (error) {
      // 静默失败
    }
  }

  /**
   * 添加用户到黑名单
   * @param {Object} user - 用户信息
   * @param {string} user.username - 用户名
   * @returns {boolean} 是否添加成功
   */
  addUser(user) {
    if (!user.username) {
      return false;
    }

    // 检查是否已存在
    if (this.isBlacklisted(user.username)) {
      return false;
    }

    const blacklistEntry = {
      userId: user.username,
      username: user.username,
      addedAt: new Date().toISOString()
    };

    this.blacklist.push(blacklistEntry);
    this.saveBlacklist();

    return true;
  }

  /**
   * 从黑名单移除用户
   * @param {string} username - 用户名
   * @returns {boolean} 是否移除成功
   */
  removeUser(username) {
    const initialLength = this.blacklist.length;
    this.blacklist = this.blacklist.filter(user => user.username !== username);

    if (this.blacklist.length < initialLength) {
      this.saveBlacklist();
      return true;
    }

    return false;
  }

  /**
   * 检查用户是否在黑名单中
   * @param {string} username - 用户名
   * @returns {boolean} 是否在黑名单中
   */
  isBlacklisted(username) {
    return this.blacklist.some(user => user.username === username);
  }

  /**
   * 获取完整黑名单
   * @returns {Array} 黑名单数组
   */
  getAllUsers() {
    return [...this.blacklist];
  }

  /**
   * 获取黑名单用户名列表
   * @returns {Array} 用户名数组
   */
  getUsernames() {
    return this.blacklist.map(user => user.username);
  }

  /**
   * 设置黑名单用户名列表
   * @param {Array} usernames - 用户名数组
   */
  setUsernames(usernames) {
    this.blacklist = usernames.map(username => ({
      userId: username,
      username: username,
      addedAt: new Date().toISOString()
    }));
    this.saveBlacklist();
  }

  /**
   * 获取黑名单统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      total: this.blacklist.length
    };
  }
}
