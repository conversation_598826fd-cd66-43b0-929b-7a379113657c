/**
 * 黑名单存储管理模块
 * 使用油猴脚本GM_setValue/GM_getValue管理黑名单数据
 */

import { GM_getValue, GM_setValue } from '$';

const BLACKLIST_KEY = 'weibo_enhancer_blacklist';

export class BlacklistStorage {
  constructor() {
    this.blacklist = this.loadBlacklist();
  }

  /**
   * 从GM storage加载黑名单
   * @returns {Array} 黑名单数组（字符串数组）
   */
  loadBlacklist() {
    try {
      // 直接存储字符串数组，每行一个用户名
      const usernames = GM_getValue(BLACKLIST_KEY, []).filter(u => u && typeof u === 'string') || [];
      console.log('Weibo Enhancer: Loaded blacklist from storage:', usernames);
      return usernames;
    } catch (error) {
      console.error('Weibo Enhancer: Failed to load blacklist:', error);
      return [];
    }
  }

  /**
   * 保存黑名单到GM storage
   */
  saveBlacklist() {
    try {
      // 直接保存字符串数组
      GM_setValue(BLACKLIST_KEY, this.blacklist);
      console.log('Weibo Enhancer: Saved blacklist to storage:', this.blacklist);
    } catch (error) {
      console.error('Weibo Enhancer: Failed to save blacklist:', error);
    }
  }

  /**
   * 添加用户到黑名单
   * @param {Object} user - 用户信息
   * @param {string} user.username - 用户名
   * @returns {boolean} 是否添加成功
   */
  addUser(user) {
    if (!user.username) {
      console.warn('Weibo Enhancer: Cannot add user without username');
      return false;
    }

    // 检查是否已存在
    if (this.isBlacklisted(user.username)) {
      console.log('Weibo Enhancer: User already blacklisted:', user.username);
      return false;
    }

    // 新增用户名添加到最上面
    this.blacklist.unshift(user.username);
    console.log('Weibo Enhancer: Added user to blacklist:', user.username);
    this.saveBlacklist();

    return true;
  }

  /**
   * 从黑名单移除用户
   * @param {string} username - 用户名
   * @returns {boolean} 是否移除成功
   */
  removeUser(username) {
    const initialLength = this.blacklist.length;
    this.blacklist = this.blacklist.filter(u => u !== username);

    if (this.blacklist.length < initialLength) {
      this.saveBlacklist();
      return true;
    }

    return false;
  }

  /**
   * 检查用户是否在黑名单中
   * @param {string} username - 用户名
   * @returns {boolean} 是否在黑名单中
   */
  isBlacklisted(username) {
    return this.blacklist.includes(username);
  }

  /**
   * 获取完整黑名单
   * @returns {Array} 黑名单数组（字符串数组）
   */
  getAllUsers() {
    return [...this.blacklist];
  }

  /**
   * 获取黑名单用户名列表
   * @returns {Array} 用户名数组
   */
  getUsernames() {
    return [...this.blacklist];
  }

  /**
   * 设置黑名单用户名列表
   * @param {Array} usernames - 用户名数组
   */
  setUsernames(usernames) {
    this.blacklist = [...usernames];
    this.saveBlacklist();
  }

  /**
   * 获取黑名单统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      total: this.blacklist.length
    };
  }
}
