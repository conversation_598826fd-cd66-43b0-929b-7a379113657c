// 创建用户屏蔽菜单
export const userMenu = document.createElement('div');
userMenu.style.cssText = `
    position: fixed;
    background: #fff;
    border: 1px solid #ccc;
    padding: 5px;
    cursor: pointer;
    z-index: 10000;
    display: none;
`;
document.body.appendChild(userMenu);

// 创建图片屏蔽菜单
export const imageMenu = document.createElement('div');
imageMenu.id = 'hupu-enhancer-img-menu';
imageMenu.style.cssText = `
    position: fixed;
    background: #ffffff;
    border: 1px solid #09f;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    z-index: 9999999;
    display: none;
    font-family: system-ui;
    color: #333;
    min-width: 160px;
    opacity: 1;
    visibility: visible;
    transition: background 0.2s;
`;
imageMenu.innerHTML = `
    <div class="menu-item" style="padding: 6px; cursor: pointer;">
        🖼️ 隐藏该图片
    </div>
`;
document.body.appendChild(imageMenu);

// 为图片菜单添加鼠标悬停效果
imageMenu.addEventListener('mouseover', () => {
    imageMenu.style.background = '#f0f8ff';
});
imageMenu.addEventListener('mouseout', () => {
    imageMenu.style.background = '#ffffff';
});

// 隐藏所有浮动菜单
export function hideMenus(e) {
    // 确保点击事件不是发生在菜单内部
    if (e && (userMenu.contains(e.target) || imageMenu.contains(e.target))) {
        return;
    }
    userMenu.style.display = 'none';
    imageMenu.style.display = 'none';
} 